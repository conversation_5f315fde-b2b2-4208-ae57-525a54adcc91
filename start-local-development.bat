@echo off
echo ========================================
echo StageMinder Local Development Startup
echo ========================================
echo.

echo Step 1: Starting Neo4j Database...
echo Please make sure Neo4j is installed and running on localhost:7687
echo Username: neo4j
echo Password: K@kapoDev@123
echo.
pause

echo Step 2: Starting Backend Server...
echo Navigating to backend directory...
cd "Build\backend"
echo Starting Spring Boot application...
start "StageMinder Backend" cmd /k "mvn spring-boot:run"
echo Backend will be available at: http://localhost:8080
echo.

echo Waiting 30 seconds for backend to start...
timeout /t 30 /nobreak

echo Step 3: Starting Frontend Server...
echo Navigating to frontend directory...
cd "..\frontend"
echo Starting Next.js development server...
start "StageMinder Frontend" cmd /k "npm run dev"
echo Frontend will be available at: http://localhost:3000
echo.

echo ========================================
echo Local Development Environment Started!
echo ========================================
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:8080
echo Database: bolt://localhost:7687
echo.
echo Test User: <EMAIL>
echo (Automatic authentication enabled)
echo.
echo IMPORTANT: In test mode, you can login with ANY email/password
echo The system will automatically <NAME_EMAIL>
echo.
echo Press any key to exit...
pause
