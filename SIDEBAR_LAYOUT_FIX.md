# Sidebar Layout Fix

## Problem
The content area (`div.MuiBox-root.css-0`) was being hidden by the left sidebar when the sidebar expanded from 100px to 300px width. The content area only had a fixed left margin of 100px, causing overlap when the sidebar expanded.

## Root Cause
- **Sidebar**: Fixed positioned, width changes from 100px (collapsed) to 300px (expanded)
- **Content Area**: Had fixed `ml-[100px]` (margin-left: 100px) regardless of sidebar state
- **Result**: When sidebar expanded to 300px, it covered 200px of the content area

## Solution
Modified the layout components to dynamically adjust the left margin based on sidebar state:

### 1. Modified `(booking)/layout.js`
```jsx
// Before
<Box className="ml-[12px] lg:ml-[100px] ">{children}</Box>

// After
<Box 
  className={`ml-[12px] transition-all duration-300 ${
    isOpened ? 'lg:ml-[300px]' : 'lg:ml-[100px]'
  }`}
>
  {children}
</Box>
```

### 2. Modified `account-sidebar.common.js`
```jsx
// Before
<Box className="lg:fixed lg:!w-[274px] !w-full lg:ml-[100px] ml-0 ...">

// After
<Box 
  className={`lg:fixed lg:!w-[274px] !w-full ml-0 ... transition-all duration-300 ${
    isOpened ? 'lg:ml-[300px]' : 'lg:ml-[100px]'
  }`}
>
```

### 3. Added Global CSS Utilities
```css
.sidebar-content {
  margin-left: 12px;
  transition: margin-left 0.3s ease;
}

@media (min-width: 1024px) {
  .sidebar-content {
    margin-left: 100px;
  }
  
  .sidebar-content.sidebar-expanded {
    margin-left: 300px;
  }
}
```

## How It Works
1. **Sidebar State**: Managed by `useAppContext()` with `isOpened` boolean
2. **Dynamic Margin**: Content area margin adjusts based on `isOpened` state
3. **Smooth Transition**: CSS transitions provide smooth animation
4. **Responsive**: Only applies on large screens (`lg:` prefix)

## Files Modified
- `Build/frontend/src/app/[locale]/(private)/(booking)/layout.js`
- `Build/frontend/src/common/account-sidebar/account-sidebar.common.js`
- `Build/frontend/src/styles/globals.css`

## Testing
1. Open the application in a browser
2. Navigate to any page with sidebar
3. Click the expand/collapse button in the sidebar
4. Verify content area adjusts its left margin smoothly
5. Ensure no content is hidden behind the sidebar

## Test File
A standalone HTML test file `test-sidebar-layout.html` is provided to demonstrate the expected behavior.

## Future Considerations
- Consider using CSS Grid or Flexbox for more robust layout management
- Add responsive breakpoints for tablet sizes
- Consider adding animation preferences for accessibility
