@echo off
echo ========================================
echo Testing StageMinder Login Configuration
echo ========================================
echo.

echo Testing backend API endpoint...
echo Making a test request to: http://localhost:8080/api/v1/public/login
echo.

curl -X POST http://localhost:8080/api/v1/public/login ^
  -H "Content-Type: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"password\":\"anypassword\"}"

echo.
echo.
echo If you see a successful response with an access token, the test mode is working!
echo If you see an error, make sure the backend is running on localhost:8080
echo.
pause
