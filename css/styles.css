body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    text-rendering: optimizeSpeed;
}

.main-container {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
    width: 100%;
    padding: 30px;
    gap: 30px;
    background: #f5f5f7;
}

.poster-container {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: center;
    min-width: 0;
    gap: 20px;
    position: relative;
}

#poster {
    width: 648px;
    height: 900px;
    background-color: white;
    position: relative;
    transform-origin: top center;
    max-width: 100%;
    max-height: 90vh;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
}

#posterCanvas {
    width: 100%;
    height: 100%;
    background-color: white;
}

.background-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 504px;
}

.background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.style-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.style-overlay {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.title-section {
    position: absolute;
    top: 520px;
    width: 648px;
}

.subtitle-section {
    position: absolute;
    top: 620px;
    width: 648px;
}

.details-section {
    position: absolute;
    top: 750px;
    width: 648px;
}

.venue-section {
    position: absolute;
    top: 840px;
    width: 648px;
}

.text-container {
    position: relative;
    max-width: 100%;
    margin: 0 auto;
    text-align: center;
}

.title-text {
    width: 100%;
    text-align: center;
    margin: 0 auto;
    white-space: nowrap;
    overflow: hidden;
}

.location-text {
    margin-top: 15px;
}

.venue-text {
    width: 100%;
    text-align: center;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.info-container {
    display: flex;
    justify-content: space-between;
    max-width: 500px;
    margin: 0 auto;
    padding: 0 20px;
}

.info-item {
    text-align: center;
    flex: 1;
    margin: 0 10px;
}

.main-title, .subtitle, .location, .info-label, .info-value, .venue-name, .venue-address, .venue-website {
    color: #ffffff;
    font-family: "Roboto Condensed", sans-serif;
    text-transform: uppercase;
}

.main-title {
    font-weight: 700;
}

.info-label {
    font-size: 20px;
}

.info-value {
    font-size: 24px;
}

.venue-name {
    font-weight: 700;
    font-size: 20px;
    letter-spacing: 1px;
}

.venue-address, .venue-website {
    font-size: 20px;
    letter-spacing: 1px;
}

.control-panel {
    width: 420px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    height: auto;
    max-height: calc(100vh - 60px);
    overflow-y: auto;
    position: sticky;
    top: 30px;
    flex-shrink: 0;
}

.control-group {
    margin-bottom: 30px;
    padding: 25px;
    border-radius: 16px;
    background: #f5f5f7;
    border: 1px solid #e5e5e7;
    transition: all 0.3s ease;
}

.control-group:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border-color: #d5d5d7;
}

.control-label {
    font-weight: 600;
    margin-bottom: 15px;
    color: #1d1d1f;
    font-size: 16px;
    letter-spacing: -0.5px;
}

.form-control {
    margin-bottom: 15px;
    border: 1px solid #d2d2d7;
    border-radius: 12px;
    padding: 12px 15px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    border-color: #0071e3;
    box-shadow: 0 0 0 4px rgba(0, 113, 227, 0.1);
    outline: none;
}

.color-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.font-color-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.font-select {
    height: 42px;
    padding: 8px 12px;
    border: 1px solid #d2d2d7;
    border-radius: 12px;
    font-size: 14px;
    background-color: #fff;
    cursor: pointer;
}

.font-select:focus {
    border-color: #0071e3;
    box-shadow: 0 0 0 4px rgba(0, 113, 227, 0.1);
    outline: none;
}

.input-full .font-select {
    margin-bottom: 10px;
}

.input-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.input-group.three-columns {
    grid-template-columns: repeat(3, 1fr);
}

.input-full {
    grid-column: 1 / -1;
}

h4.mb-4 {
    color: #1d1d1f;
    font-weight: 600;
    margin-bottom: 25px;
    font-size: 24px;
    letter-spacing: -0.5px;
    border-bottom: 1px solid #d2d2d7;
    padding-bottom: 20px;
}

.upload-label {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    background: #0071e3;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    gap: 8px;
}

.upload-label:hover {
    background: #0077ED;
}

.preview-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 10px 0;
    min-height: 100px;
}

.preview-item {
    position: relative;
    width: 120px;
    height: 120px;
    border: 2px solid #d2d2d7;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f7;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.preview-delete {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: rgba(255, 59, 48, 0.9);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.preview-delete:hover {
    background: rgb(255, 59, 48);
    transform: scale(1.1);
}

#imageInput {
    display: none;
}

.char-counter {
    font-size: 12px;
    color: #86868b;
    text-align: right;
    margin-top: -12px;
    margin-bottom: 15px;
    font-weight: 500;
}

.char-counter.near-limit {
    color: #f7a000;
}

.char-counter.at-limit {
    color: #ff3b30;
}

#resetButton {
    background: #1d1d1f;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 15px;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 10px;
}

#resetButton:hover {
    background: #2d2d2f;
    transform: translateY(-1px);
}

/* Custom scrollbar for control panel */
.control-panel::-webkit-scrollbar {
    width: 8px;
}

.control-panel::-webkit-scrollbar-track {
    background: #f5f5f7;
    border-radius: 4px;
}

.control-panel::-webkit-scrollbar-thumb {
    background: #d2d2d7;
    border-radius: 4px;
}

.control-panel::-webkit-scrollbar-thumb:hover {
    background: #86868b;
}

/* Modal Styles */
.crop-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow: auto;
}

.crop-modal-content {
    position: relative;
    background-color: #fefefe;
    margin: 2% auto;
    padding: 20px;
    border-radius: 12px;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
}

.crop-container {
    height: calc(90vh - 150px);
    background-color: #333;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.crop-container img {
    max-width: 100%;
    max-height: 100%;
}

.crop-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 10px 0;
}

.crop-button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background-color: #0071e3;
    color: white;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.crop-button:hover {
    background-color: #0077ED;
}

.close-modal {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
    z-index: 1;
}

.close-modal:hover {
    color: #000;
}

.download-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0071e3;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 648px;
    gap: 8px;
    letter-spacing: -0.5px;
}

.download-button:hover {
    background: #0077ED;
    transform: translateY(-1px);
}

.download-button:active {
    transform: translateY(0);
    background: #0062c1;
}

.social-buttons {
    display: flex;
    flex-direction: row;
    gap: 15px;
    margin-top: 15px;
    width: 100%;
    max-width: 648px;
}

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 25px;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 16px;
    gap: 12px;
    flex: 1;
    text-align: center;
}

.social-button i {
    font-size: 20px;
}

.facebook-button {
    background: #1877f2;
}

.facebook-button:hover {
    background: #0d6efd;
    transform: translateY(-2px);
}

.instagram-button {
    background: #e4405f;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.instagram-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1400px) {
    .control-panel {
        width: 350px;
        padding: 25px;
    }

    .control-group {
        padding: 20px;
    }
}

@media (max-width: 1200px) {
    .control-panel {
        width: 315px;
        padding: 20px;
    }

    .control-group {
        padding: 15px;
        margin-bottom: 20px;
    }

    .form-control {
        padding: 10px 12px;
    }
}

@media (max-width: 992px) {
    .main-container {
        flex-direction: column;
        padding: 20px;
    }

    .control-panel {
        width: 100%;
        max-width: 800px;
        margin: 20px auto;
        position: relative;
        top: 0;
    }

    .input-group.three-columns {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 15px;
    }

    .input-group, .input-group.three-columns {
        grid-template-columns: repeat(2, 1fr);
    }

    .color-row {
        grid-template-columns: 1.5fr 1fr;
    }

    .font-color-controls {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .download-button {
        font-size: 15px;
        padding: 12px 24px;
    }

    .social-buttons {
        flex-direction: column;
    }

    .poster-container {
        flex-direction: column;
    }

    .poster-toolbar {
        flex-direction: row;
        width: 100%;
        overflow-x: auto;
        padding: 10px;
    }

    .toolbar-group {
        flex-direction: row;
        padding-bottom: 0;
        border-bottom: none;
        border-right: 1px solid #e5e5e7;
        padding-right: 15px;
    }

    .toolbar-group:last-child {
        border-right: none;
        padding-right: 0;
    }
}

@media (max-width: 576px) {
    .main-container {
        padding: 10px;
    }

    .control-panel {
        padding: 15px;
    }

    .control-group {
        padding: 12px;
    }

    .input-group, .input-group.three-columns, .color-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .download-button {
        font-size: 14px;
        padding: 10px 20px;
    }
}

.canvas-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 15px 0;
}

.canvas-controls button {
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.canvas-controls button i {
    font-size: 16px;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow: auto;
}

.modal-content {
    position: relative;
    background-color: #fefefe;
    margin: 2% auto;
    padding: 25px;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
}

.size-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.size-icon {
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f7;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.size-icon i {
    font-size: 48px;
    color: #1d1d1f;
    transition: all 0.3s ease;
}

.size-option {
    background: #ffffff;
    border: 2px solid #e5e5e7;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.size-option:hover {
    border-color: #0071e3;
    background: #f0f0f2;
    transform: translateY(-2px);
}

.size-option:hover .size-icon {
    background: #ffffff;
    border-color: #0071e3;
}

.size-option:hover .size-icon i {
    color: #0071e3;
    transform: scale(1.1);
}

.size-option.selected {
    border-color: #0071e3;
    background: #f0f7ff;
    box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.2);
}

.size-option.selected .size-icon {
    background: #ffffff;
    border-color: #0071e3;
}

.size-option.selected .size-icon i {
    color: #0071e3;
}

.size-option h5 {
    margin: 0 0 5px;
    color: #1d1d1f;
    font-size: 16px;
    font-weight: 600;
}

.size-option p {
    margin: 0;
    color: #86868b;
    font-size: 14px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e5e5e7;
}

.modal-footer button {
    padding: 8px 20px;
    border-radius: 8px;
    font-size: 14px;
}

.poster-toolbar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 15px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.toolbar-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e7;
}

.toolbar-group:last-child {
    padding-bottom: 0;
    border-bottom: none;
}

.toolbar-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background: #f5f5f7;
    color: #1d1d1f;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 80px;
    gap: 8px;
}

.toolbar-btn i {
    font-size: 20px;
}

.toolbar-btn span {
    font-size: 12px;
    font-weight: 500;
}

.toolbar-btn:hover {
    background: #e5e5e7;
    transform: translateY(-1px);
}

.toolbar-btn.download-btn {
    background: #0071e3;
    color: white;
}

.toolbar-btn.download-btn:hover {
    background: #0077ED;
}

.toolbar-btn.share-btn.facebook {
    background: #1877f2;
    color: white;
}

.toolbar-btn.share-btn.facebook:hover {
    background: #0d6efd;
}

.toolbar-btn.share-btn.instagram {
    background: #e4405f;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: white;
}

.toolbar-btn.share-btn.instagram:hover {
    opacity: 0.9;
}

#deleteSelected {
    background: #ff3b30;
    color: white;
}

#deleteSelected:hover {
    background: #ff453a;
}

/* Style Layer Controls */
.style-layer-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.style-layer-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.style-layer-row button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.style-layer-row button:hover {
    background: #0056b3;
}

.style-layer-row button:active {
    background: #004085;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
        padding: 15px;
        gap: 20px;
    }

    .poster-container {
        flex-direction: column;
        align-items: center;
    }

    .poster-toolbar {
        order: 2;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        margin-top: 20px;
    }

    .toolbar-group {
        flex-direction: row;
        gap: 10px;
    }

    .toolbar-btn {
        min-width: auto;
        padding: 8px 12px;
    }

    .toolbar-btn span {
        display: none;
    }

    .control-panel {
        order: 3;
        max-width: none;
        width: 100%;
    }

    #poster {
        order: 1;
        max-width: 90vw;
        max-height: 60vh;
    }

    .color-row {
        flex-direction: column;
        gap: 10px;
    }

    .font-color-controls {
        flex-direction: column;
        gap: 10px;
    }

    .three-columns {
        flex-direction: column;
        gap: 10px;
    }

    .three-columns > div {
        width: 100%;
    }
}