<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Event Poster</title>
	
	<!-- Facebook Open Graph tags -->
	<meta property="og:url" content="https://your-domain.com/poster" />
	<meta property="og:type" content="website" />
	<meta property="og:title" content="Event Poster" />
	<meta property="og:description" content="Join us for an amazing event!" />
	<meta property="og:image" id="og-image" content="" />
	
	<link href="../css/styles.css" rel="stylesheet" type="text/css" />
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.css" rel="stylesheet">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css" rel="stylesheet">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
	<script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
</head>

<body>
	<div class="main-container">
		<div class="poster-container">
			<div class="poster-toolbar">
				<div class="toolbar-group">
					<button class="toolbar-btn" id="deleteSelected" title="Delete Selected">
						<i class="fas fa-trash"></i>
						<span>Delete</span>
					</button>
					<button class="toolbar-btn" id="bringForward" title="Bring Forward">
						<i class="fas fa-arrow-up"></i>
						<span>Bring Forward</span>
					</button>
					<button class="toolbar-btn" id="sendBackward" title="Send Backward">
						<i class="fas fa-arrow-down"></i>
						<span>Send Backward</span>
					</button>
					<button class="toolbar-btn" id="resizeButton" title="Resize Poster">
						<i class="fas fa-expand"></i>
						<span>Resize</span>
					</button>
				</div>
				<div class="toolbar-group">
					<button class="toolbar-btn download-btn" onclick="downloadPoster()" title="Download Poster">
						<i class="fas fa-cloud-download-alt"></i>
						<span>Download</span>
					</button>
				</div>
				<div class="toolbar-group">
					<button class="toolbar-btn share-btn facebook" onclick="shareToFacebook()" title="Share to Facebook">
						<i class="fab fa-facebook-f"></i>
						<span>Facebook</span>
					</button>
					<button class="toolbar-btn share-btn instagram" onclick="shareToInstagram()" title="Share to Instagram">
						<i class="fab fa-instagram"></i>
						<span>Instagram</span>
					</button>
				</div>
			</div>
			<div id="poster">
				<canvas id="posterCanvas" width="648" height="900"></canvas>
			</div>
		</div>

		<div class="control-panel">
			<h4 class="mb-4">Poster Controls</h4>
			
			<div class="control-group">
				<label class="control-label">Background Image</label>
				<div class="image-upload-container">
					<label class="upload-label" for="imageInput">
						<i class="fas fa-cloud-upload-alt"></i>
						Choose Image
					</label>
					<input type="file" id="imageInput" accept="image/*">
					<div class="preview-container">
						<!-- <img id="imagePreview" src="" alt="Preview"> -->
					</div>
				</div>
			</div>

			<div class="control-group">
				<label class="control-label">Style Layer (LIVE MUSIC)</label>
				<div class="style-layer-controls">
					<div class="style-layer-row">
						<button class="btn btn-sm" id="toggleStyleLayer">Hide Style Layer</button>
					</div>
					<div class="font-size-control">
						<label>Opacity: <span id="styleLayerOpacityValue">80</span>%</label>
						<input type="range" id="styleLayerOpacity" min="0" max="100" value="80" class="size-slider">
					</div>
				</div>
			</div>

			<div class="control-group">
				<label class="control-label">Main Title</label>
				<div class="color-row">
					<input type="text" class="form-control" id="mainTitle" value="WILD HARMONIES" data-target="mainTitle">
					<div class="font-color-controls">
						<select class="form-control font-select" id="mainTitleFont" data-target="mainTitle">
							<option value="Arial">Arial</option>
							<option value="Times New Roman">Times New Roman</option>
							<option value="Helvetica">Helvetica</option>
							<option value="Georgia">Georgia</option>
							<option value="Verdana">Verdana</option>
							<option value="Impact">Impact</option>
						</select>
						<input type="text" class="form-control" id="mainTitleColor" data-type="color" data-target="mainTitle" value="#4A90E2">
					</div>
				</div>
				<div class="font-size-control">
					<label>Font Size: <span id="mainTitleSizeValue">70</span>px</label>
					<input type="range" id="mainTitleSize" data-target="mainTitle" min="20" max="120" value="70" class="size-slider">
				</div>
			</div>

			<div class="control-group">
				<label class="control-label">Subtitle</label>
				<div class="color-row">
					<input type="text" class="form-control" id="subtitle" value="A FULL NIGHT OF ROCK, LIVE IN">
					<div class="font-color-controls">
						<select class="form-control font-select" id="subtitleFont">
							<option value="Arial">Arial</option>
							<option value="Times New Roman">Times New Roman</option>
							<option value="Helvetica">Helvetica</option>
							<option value="Georgia">Georgia</option>
							<option value="Verdana">Verdana</option>
							<option value="Impact">Impact</option>
						</select>
						<input type="text" class="form-control" id="subtitleColor" data-type="color" value="#4A90E2">
					</div>
				</div>
				<div class="font-size-control">
					<label>Font Size: <span id="subtitleSizeValue">32</span>px</label>
					<input type="range" id="subtitleSize" min="12" max="60" value="32" class="size-slider">
				</div>
			</div>

			<div class="control-group">
				<label class="control-label">Location</label>
				<div class="color-row">
					<input type="text" class="form-control" id="location" value="OTTAWA">
					<div class="font-color-controls">
						<select class="form-control font-select" id="locationFont">
							<option value="Arial">Arial</option>
							<option value="Times New Roman">Times New Roman</option>
							<option value="Helvetica">Helvetica</option>
							<option value="Georgia">Georgia</option>
							<option value="Verdana">Verdana</option>
							<option value="Impact">Impact</option>
						</select>
						<input type="text" class="form-control" id="locationColor" data-type="color" value="#4A90E2">
					</div>
				</div>
				<div class="font-size-control">
					<label>Font Size: <span id="locationSizeValue">50</span>px</label>
					<input type="range" id="locationSize" min="20" max="80" value="50" class="size-slider">
				</div>
			</div>

			<div class="control-group">
				<label class="control-label">Event Details</label>
				<div class="input-group three-columns">
					<div>
						<input type="text" class="form-control" id="eventDate" value="SEP 25, 2025">
					</div>
					<div>
						<input type="text" class="form-control" id="eventTime" value="9:00 PM">
					</div>
					<div>
						<input type="text" class="form-control" id="eventPrice" value="$23">
					</div>
					<div class="input-full">
						<select class="form-control font-select" id="eventDetailsFont">
							<option value="Arial">Arial</option>
							<option value="Times New Roman">Times New Roman</option>
							<option value="Helvetica">Helvetica</option>
							<option value="Georgia">Georgia</option>
							<option value="Verdana">Verdana</option>
							<option value="Impact">Impact</option>
						</select>
						<input type="text" class="form-control" id="eventDetailsColor" data-type="color" value="#4A90E2">
					</div>
				</div>
				<div class="font-size-control">
					<label>Font Size: <span id="eventDetailsSizeValue">22</span>px</label>
					<input type="range" id="eventDetailsSize" min="12" max="40" value="22" class="size-slider">
				</div>
			</div>

			<div class="control-group">
				<label class="control-label">Venue Information</label>
				<div class="color-row">
					<input type="text" class="form-control" id="venueName" value="HOUSE OF TARG">
					<div class="font-color-controls">
						<select class="form-control font-select" id="venueInfoFont">
							<option value="Arial">Arial</option>
							<option value="Times New Roman">Times New Roman</option>
							<option value="Helvetica">Helvetica</option>
							<option value="Georgia">Georgia</option>
							<option value="Verdana">Verdana</option>
							<option value="Impact">Impact</option>
						</select>
						<input type="text" class="form-control" id="venueInfoColor" data-type="color" value="#4A90E2">
					</div>
				</div>
				<input type="text" class="form-control" id="venueAddress" value="1077 BANK ST, OTTAWA">
				<input type="text" class="form-control" id="venueWebsite" value="HOUSEOFTARG.COM">
				<div class="font-size-control">
					<label>Font Size: <span id="venueInfoSizeValue">18</span>px</label>
					<input type="range" id="venueInfoSize" min="10" max="30" value="18" class="size-slider">
				</div>
			</div>

			<div class="control-group">
				<button id="resetButton" class="btn">Reset All Changes</button>
			</div>
		</div>
	</div>

	<!-- Crop Modal -->
	<div id="cropModal" class="crop-modal">
		<div class="crop-modal-content">
			<span class="close-modal">&times;</span>
			<div class="crop-container">
				<img id="cropImage" src="" alt="Crop">
			</div>
			<div class="crop-controls">
				<button class="crop-button" id="rotateLeft">↺ Rotate Left</button>
				<button class="crop-button" id="rotateRight">↻ Rotate Right</button>
				<button class="crop-button" id="cropButton">Apply Crop</button>
				<button class="crop-button" id="cancelCrop">Cancel</button>
			</div>
		</div>
	</div>

	<!-- Resize Modal -->
	<div id="resizeModal" class="modal">
		<div class="modal-content">
			<span class="close-modal">&times;</span>
			<h4>Choose Poster Size</h4>
			<div class="size-options">
				<div class="size-option" data-width="648" data-height="900">
					<div class="size-icon">
						<i class="fas fa-file-image"></i>
					</div>
					<h5>Standard Size</h5>
					<p>648 × 900 px</p>
				</div>
				<div class="size-option" data-width="1200" data-height="630">
					<div class="size-icon">
						<i class="fab fa-facebook-square"></i>
					</div>
					<h5>Facebook Shared Image</h5>
					<p>1200 × 630 px</p>
				</div>
				<div class="size-option" data-width="1080" data-height="1350">
					<div class="size-icon">
						<i class="fab fa-instagram"></i>
					</div>
					<h5>Instagram Portrait Post</h5>
					<p>1080 × 1350 px</p>
				</div>
			</div>
			<div class="modal-footer">
				<button class="btn btn-secondary" id="cancelResize">Cancel</button>
				<button class="btn btn-primary" id="applyResize">Apply Size</button>
			</div>
		</div>
	</div>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.js"></script>
	<script src="../js/poster.js"></script>
	<script>
	document.addEventListener('DOMContentLoaded', function() {
		// Initialize font change handlers
		document.querySelectorAll('.font-select').forEach(select => {
			const target = select.dataset.target;
			handleFontChange(select, target);
		});

		// Initialize size change handlers
		document.querySelectorAll('.size-slider').forEach(slider => {
			const target = slider.dataset.target;
			slider.addEventListener('input', function() {
				updateText(target, undefined, { fontSize: parseInt(this.value) });
			});
		});

		// Initialize text input handlers
		document.querySelectorAll('input[type="text"]:not([data-type="color"])').forEach(input => {
			const target = input.dataset.target;
			if (target) {
				input.addEventListener('input', function() {
					const objects = canvas.getObjects();
					const textObj = objects.find(o => o.type === 'text' && o.text === this.dataset.oldText);
					if (textObj) {
						textObj.set('text', this.value);
						canvas.requestRenderAll();
					}
					this.dataset.oldText = this.value;
				});
				input.dataset.oldText = input.value;
			}
		});
	});
	</script>
</body>

</html>