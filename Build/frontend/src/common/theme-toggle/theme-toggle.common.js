"use client";
import React from "react";
import { IconButton } from "@mui/material";
import { useThemeContext } from "@/context/theme/theme.context";
import SunIcon from "@/assets/svg/SunIcon.svg";
import MoonIcon from "@/assets/svg/MoonIcon.svg";

const ThemeToggle = () => {
  const { isDarkMode, toggleTheme } = useThemeContext();

  return (
    <IconButton 
      onClick={toggleTheme}
      sx={{ padding: 0 }}
      className="!transition-all !duration-300 hover:!scale-110"
      aria-label={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
    >
      {isDarkMode ? (
        <SunIcon className="!w-6 !h-6 !cursor-pointer !transition-all !duration-300" />
      ) : (
        <MoonIcon className="!w-6 !h-6 !cursor-pointer !transition-all !duration-300" />
      )}
    </IconButton>
  );
};

export default ThemeToggle;
