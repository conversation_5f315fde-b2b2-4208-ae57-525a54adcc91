"use client";
import { Box, IconButton, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { Button } from "@/component";
import Notification from "@/assets/svg/Notification.svg";
import OutlinedUser from "@/assets/svg/OutlinedUser.svg";
import Link from "next/link";
import FilledHeart from "@/assets/svg/FilledHeart.svg";
import { useLocale, useTranslations } from "next-intl";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useSelector } from "react-redux";
import LogoComponent from "../logo-component/logo-component.common";
import NotificationPopup from "../notifications-popup/notifications-popup.common";
import ThemeToggle from "../theme-toggle/theme-toggle.common";

const Navbar = ({}) => {
  const s = useTranslations("Header");
  const lang = useLocale();

  const [openPopup, setOpenPopup] = useState(false);
  const handleClosePopup = () => {
    setOpenPopup(false);
  };
  const handleOpenPopup = () => {
    setOpenPopup(true);
  };

  const { token } = useSelector((state) => state.login);

  useEffect(() => {
    //openPopup && dispatch(getSavedSearches());
  }, [openPopup]);
  const { instantMessage } = useSelector((state) => state.instantMessage);

  return (
    <Box className="!flex !h-[72px] !items-center !justify-between !w-full lg:px-10 px-5 !bg-[--bg-color] lg:!border-b !border-b-[--divider-color]">
      <Box className="!flex lg:gap-8 gap-2 !items-center !w-full">
        <Link href={token ? `/${lang}/search?profileType=ACT_PROFILE` : `/${lang}`}>
          {/* <Logo className="!w-[70px] !h-6" /> */}
          <LogoComponent />
        </Link>
        {/* {!isMobile ? (
          <Searchbar placeholder={t("lookingFor")} />
        ) : (
          <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
            {title}
          </Typography>
        )} */}
      </Box>
      <Box className="!flex !items-center lg:gap-x-6 gap-3">
        <ThemeToggle />
        {
          token && (
            <Link href={`/${lang}/favourites/acts`}>
              <FilledHeart className="!w-6 !h-6 !cursor-pointer" />
            </Link>
          )
          // ) : (
          //   <Link href={`/${lang}/favourites/acts`}>
          //     <LikeIcon className="!w-6 !h-6 !cursor-pointer" />
          //   </Link>
          // )
        }
        {token && (
          <IconButton sx={{ padding: 0 }} onClick={handleOpenPopup} className="relative">
            <Notification className="!w-6 !h-6 !cursor-pointer" />
            {instantMessage.length > 0 && (
              <Typography className="text-[12px] text-[--bg-color] font-craftWorkHeavy bg-[--text-color] px-1 rounded-full absolute top-[-8px] right-[-5px]">
                {instantMessage.length > 0 &&
                  instantMessage.filter((item) => item?.dismissed === false && item?.seen === false)
                    .length}
              </Typography>
            )}
          </IconButton>
        )}
        {!token || token === null ? (
          <Link href={`/${lang}/login`}>
            <Button className="!bg-[--text-color] !flex !gap-x-2 !px-2 !py-3" sx={{ padding: 0 }}>
              <Typography className="!normal-case !hidden lg:!inline md:!inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {s("Register/Login")}
              </Typography>
              <ArrowSouthEast alt="arrow" />
            </Button>
          </Link>
        ) : (
          <Link href={`/${lang}/user/account-information`}>
            <OutlinedUser className="!w-6 !h-6 !cursor-pointer" />
          </Link>
        )}
      </Box>
      <NotificationPopup open={openPopup} handleClose={handleClosePopup} />
    </Box>
  );
};

export default Navbar;
