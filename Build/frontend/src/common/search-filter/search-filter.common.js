"use client";
import { Clear, North, RadioButtonUnchecked, South, SouthEast } from "@mui/icons-material";
import {
  <PERSON>,
  Divider,
  Drawer,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import DocumentSvg from "@/assets/svg/Document.svg";
import React, { useEffect, useMemo, useState } from "react";
import LocationSvg from "@/assets/svg/Location.svg";
import { Button, CheckBox } from "@/component";
import { Controller, useForm } from "react-hook-form";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { useTranslations } from "next-intl";
import Rating from "@/component/rating/rating.components";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import AutoCompleteLocation from "@/component/autocomplete/autocomplete-location.component";
import { useRouter, useSearchParams } from "next/navigation";
import { formatLocation, setLocalStorage, getLocalStorage } from "@/utils";
import { useDispatch, useSelector } from "react-redux";
import { gerActTypes, getMusicGenre } from "@/store/slice/act/act.slice";
import { appendQueryParams, removeQueryParams, updateQueryParams } from "@/utils/queryparams";
import moment from "moment";
import queryString from "query-string";
// import MapImage from "@/assets/png/MapImage.png";
// import ProfilecardImage from "@/assets/png/ProfilecardImage.png";
// import MapIcon from "@/assets/svg/MapIcon.svg";
import Searchbar from "../searchbar/searchbar.common";
import { useDebounce } from "use-debounce";
import { getDefaultLocation } from "@/store/slice/common/search.slice";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const SearchFilter = ({ open, handleClose, setOpenSaveSearch }) => {
  const [musicGenres, setMusicGenres] = useState([]);
  const [entertainmentTypes, setEntertainmentTypes] = useState([]);
  const searchParams = useSearchParams();
  const urlData = useMemo(() => {
    return Object.fromEntries(searchParams.entries());
  }, [searchParams]);
  const [inputValue, setInputValue] = useState("");
  const [debounceSearch] = useDebounce(inputValue, 1000);
  const parsedQueryParams =
    typeof window !== "undefined" && queryString.parse(window?.location?.search);

  const t = useTranslations("act");
  const n = useTranslations("navbar");

  const s = useTranslations("contractsFilter");
  const p = useTranslations("leaveFeedback");
  //const r = useTranslations("editActCommon");
  const a = useTranslations("searchFilter");
  const [showAllReviews, setShowAllReviews] = useState(
    getLocalStorage("showAllReviewsEntertainment") || false,
  );
  const [showAllReviewsMusicGenre, setShowAllReviewsMusicGenre] = useState(
    getLocalStorage("showAllReviewsMusicGenre") || false,
  );
  const [locationValue, setLocationValue] = useState("");
  const cityParam = searchParams.get("city") || "";
  const stateParam = searchParams.get("state") || "";
  const countryParam = searchParams.get("country") || "";
  const distanceParam = searchParams.get("distance") || "";
  const profileType = searchParams.get("profileType") || 0;
  const overallRating = searchParams.get("overallRating") || "";
  //const [searchDateType, setSearchDateType] = useState("");
  const dispatch = useDispatch();
  const { token } = useSelector((state) => state.login);
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [filterDisplay, setFilterDisplay] = useState({
    location: true,
    date: true,
    rating: true,
    entertainmentType: true,
    musicGenres: true,
    distance: true,
  });
  useEffect(() => {
    if (cityParam || stateParam || countryParam) {
      setLocationValue({
        city: cityParam,
        state: stateParam,
        country: countryParam,
      });
    }
  }, [cityParam, stateParam, countryParam]);

  useEffect(() => {
    if (distanceParam) {
      setInputValue(distanceParam);
    }
  }, [distanceParam]);

  useEffect(() => {
    appendQueryParams({ distance: debounceSearch }, router, searchParams);
  }, [debounceSearch]);

  useEffect(() => {
    if (locationValue) {
      appendQueryParams(
        {
          city: (locationValue.city === "null" ? "" : locationValue.city) || "",
          state: (locationValue.state === "null" ? "" : locationValue.state) || "",
          country: (locationValue.country === "null" ? "" : locationValue.country) || "",
          distance: inputValue || 100,
        },
        router,
        searchParams,
      );
    }
  }, [locationValue]);

  const ratingValue = [
    {
      value: "5",
      title: "5",
    },
    {
      value: "4",
      title: a("4AndMore"),
    },
    {
      value: "3",
      title: a("3AndMore"),
    },
    {
      value: "2",
      title: a("2AndMore"),
    },
    {
      value: "1",
      title: a("1AndMore"),
    },
    {
      value: "0",
      title: a("withoutRating"),
    },
  ];

  //const label = ["TODAY", "THIS_WEEK", "NEXT_14_DAYS", "CUSTOM"];
  const label = [
    { value: "TODAY", title: a("today") },
    { value: "THIS_WEEK", title: a("thisWeek") },
    { value: "NEXT_14_DAYS", title: a("thisMonth") },
    { value: "CUSTOM", title: a("custom") },
  ];
  const { control, watch, setValue } = useForm({
    defaultValues: {
      searchDateType: urlData?.searchDateType || "",
      overallRating: overallRating || "",
    },
  });
  const searchDateType = watch("searchDateType");
  const handleShowMore = () => {
    setLocalStorage("showAllReviewsEntertainment", !showAllReviews);
    setShowAllReviews(!showAllReviews);
  };

  const handleShowMoreMusicGenre = () => {
    setLocalStorage("showAllReviewsMusicGenre", !showAllReviewsMusicGenre);
    setShowAllReviewsMusicGenre(!showAllReviewsMusicGenre);
  };

  const [dateRange, setDateRange] = useState([
    urlData?.startDate || null,
    urlData?.endDate || null,
  ]);
  const [startDate, endDate] = dateRange;

  useEffect(() => {
    dispatch(gerActTypes());
  }, []);
  const { actTypes } = useSelector((state) => state.act);
  const entertainmentTypeParam = searchParams.getAll("entertainmentType").join(",") || "";

  useEffect(() => {
    setEntertainmentTypes(entertainmentTypeParam);
    entertainmentTypeParam?.includes("Live Music")
      ? dispatch(getMusicGenre())
          .unwrap()
          .then((res) => {
            setMusicGenres(res.data.genreList);
          })
      : setMusicGenres([]);
  }, [entertainmentTypeParam]);

  useEffect(() => {
    dispatch(getDefaultLocation())
      .unwrap()
      .then((data) => {
        if (data?.data) {
          if (data.data.cityName && data.data.countryName && data.data.stateName) {
            setLocationValue({
              city: data.data.cityName || "",
              country: data.data.countryName || "",
              state: data.data.stateName || "",
            });
          }
        }
      });
  }, []);

  const content = (
    <>
      <Box
        className={` ${token ? "lg:pl-8 lg:min-w-[395px]" : "lg:pl-8 lg:min-w-[320px]"} w-full px-4 lg:px-0 pb-24 lg:pb-0`}
      >
        <Box className="flex justify-between lg:hidden">
          <Typography className="text-[--text-color] CraftworkGroteskHeavy text-2xl">
            {t("sortBy")}
          </Typography>
          <Button className="flex gap-1 !normal-case" onClick={() => setOpenSaveSearch()}>
            <DocumentSvg className="text-2xl" />
            <Typography className="text-[--text-color] underline text-sm CraftworkGroteskHeavy">
              {a("saveSearch")}
            </Typography>
          </Button>
        </Box>
        <Searchbar placeholder={n("lookingFor")} />

        {filterDisplay?.location && (
          <Box>
            <Box className="flex justify-between">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                {a("location")}
              </Typography>
              <IconButton>
                <Clear
                  className="text-[--text-color] text-lg"
                  onClick={() => {
                    setLocationValue("");
                    removeQueryParams(["city", "state", "country"], router, searchParams);

                    setFilterDisplay((prev) => {
                      return {
                        ...prev,
                        location: true,
                      };
                    });
                  }}
                />
              </IconButton>
            </Box>
            <AutoCompleteLocation
              value={locationValue}
              setValue={setLocationValue}
              setClose={() => {}}
              locationIcon={<LocationSvg className="text-2xl" />}
              textFieldClass="border !border-[--text-color] rounded-[2px]"
              className="w-full"
            />
            {locationValue && (
              <Box className="flex flex-wrap gap-2 my-3">
                <Box className="flex gap-x-2 bg-[--divider-color] rounded-[2px] px-[10px] py-[5px]">
                  <Typography className="Poppins400 text-[--text-color] text-sm">
                    {formatLocation(locationValue)}
                  </Typography>
                  <Clear
                    className="text-[--text-color] text-lg"
                    onClick={() => {
                      removeQueryParams(["city", "state", "country"], router, searchParams);
                    }}
                  />
                </Box>
              </Box>
            )}
            <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} className="my-2" />
          </Box>
        )}

        {filterDisplay?.distance && (
          <Box>
            <Box className="flex justify-between">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                Distance (KM)
              </Typography>
              <IconButton>
                <Clear
                  className="text-[--text-color] text-lg"
                  onClick={() => {
                    setInputValue("");

                    removeQueryParams(["distance"], router, searchParams);
                  }}
                />
              </IconButton>
            </Box>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  type="number"
                  size="small"
                  {...field}
                  value={inputValue}
                  inputProps={{
                    min: 0,
                    onChange: (e) => {
                      const maxLength = Math.pow(10, ACT_CONSTANTS.HOME.DISTANCE_MAX_LENGTH) - 1;
                      if (e.target.value > maxLength) {
                        e.target.value = e.target.value.slice(0, String(maxLength).length);
                      }
                    },
                  }}
                  placeholder="Distance"
                  onChange={(e) => {
                    if (!isNaN(e.target.value) && Number(e.target.value) < 0) {
                      // Prevent negative input
                      return;
                    } else {
                      setInputValue(e.target.value);
                    }
                  }}
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& .MuiOutlinedInput-root": {
                      color: "var(--text-color)",
                      fontFamily: "var(--craftWorkRegular)",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                  className="!border !w-full !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !mb-3"
                />
              )}
            />
          </Box>
        )}

        {filterDisplay?.date && profileType === "EVENT_PROFILE" && (
          <Box>
            <Box className="flex justify-between">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                {s("date")}
              </Typography>
              <IconButton>
                <Clear
                  className="text-[--text-color] text-lg"
                  onClick={() => {
                    setDateRange([null, null]);
                    setValue("searchDateType", "");
                    removeQueryParams(
                      ["searchDateType", "startDate", "endDate"],
                      router,
                      searchParams,
                    );
                  }}
                />
              </IconButton>
            </Box>
            <Controller
              name="searchDateType"
              control={control}
              defaultValue={""}
              render={({ field }) => (
                <RadioGroup
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    if (e.target.value === "TODAY") {
                      setDateRange([new Date(), new Date()]);
                      appendQueryParams(
                        {
                          startDate: moment(new Date()).format("YYYY-MM-DD"),
                          endDate: moment(new Date()).format("YYYY-MM-DD"),
                          searchDateType: e.target.value,
                        },
                        router,
                        searchParams,
                      );
                    }
                    if (e.target.value === "THIS_WEEK") {
                      const startOfWeek = moment().startOf("week").toDate();
                      const endOfWeek = moment().endOf("week").toDate();
                      appendQueryParams(
                        {
                          startDate: moment(startOfWeek).format("YYYY-MM-DD"),
                          endDate: moment(endOfWeek).format("YYYY-MM-DD"),
                          searchDateType: e.target.value,
                        },
                        router,
                        searchParams,
                      );
                    }
                    if (e.target.value === "NEXT_14_DAYS") {
                      //const startOfMonth = moment().startOf("month").toDate();
                      //const endOfMonth = moment().endOf("month").toDate();

                      const startOfMonth = moment().toDate();
                      const endOfMonth = moment().add(14, "days").toDate();

                      appendQueryParams(
                        {
                          startDate: moment(startOfMonth).format("YYYY-MM-DD"),
                          endDate: moment(endOfMonth).format("YYYY-MM-DD"),
                          searchDateType: e.target.value,
                        },
                        router,
                        searchParams,
                      );
                    }
                  }}
                >
                  {label.map((data, id) => (
                    <FormControlLabel
                      key={data.value}
                      value={data.value}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <Typography
                          className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                          htmlFor={`radio-${id}`}
                        >
                          {data.title}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
            <ReactDatePicker
              selectsRange={true}
              startDate={startDate}
              endDate={endDate}
              disabled={searchDateType !== "CUSTOM"}
              placeholderText="mm/dd/yy - mm/dd/yy"
              onChange={(update) => {
                setDateRange(update);
                if (update[0] && update[1]) {
                  appendQueryParams(
                    {
                      startDate: moment(update[0]).format("YYYY-MM-DD"),
                      endDate: moment(update[1]).format("YYYY-MM-DD"),
                      searchDateType: searchDateType,
                    },
                    router,
                    searchParams,
                  );
                }
              }}
              className={
                searchDateType === "CUSTOM"
                  ? "react-date-picker-class cursor-pointer"
                  : "react-date-picker-class cursor-not-allowed"
              }
            />
            <Divider
              sx={{ borderTop: "thin solid var(--divider-color)" }}
              className="w-full mt-4"
            />
          </Box>
        )}
        {filterDisplay?.entertainmentType &&
          profileType === "ACT_PROFILE" &&
          actTypes?.length > 0 && (
            <Box className="flex justify-between mt-2">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                {a("entertainmentType")}
              </Typography>
              <IconButton>
                <Clear
                  className="text-[--text-color] text-lg"
                  onClick={() => {
                    removeQueryParams(["entertainmentType"], router, searchParams);
                  }}
                />
              </IconButton>
            </Box>
          )}
        {
          //filterDisplay?.entertainmentType &&
          profileType === "ACT_PROFILE" &&
            (showAllReviews ? actTypes : actTypes?.slice(0, 4))?.map((data, index) => (
              <Controller
                key={index}
                name="entertainmentType"
                control={control}
                render={({ field }) => (
                  <Box key={index} className="flex items-center !mb-2">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={entertainmentTypes?.includes(data.name)}
                      value={data.name}
                      onChange={(e) => {
                        field.onChange(e.target.checked);
                        e.target.checked
                          ? appendQueryParams(
                              {
                                entertainmentType: [e.target.value].filter(Boolean),
                              },
                              router,
                              searchParams,
                            )
                          : updateQueryParams(
                              "entertainmentType",
                              [e.target.value],
                              router,
                              searchParams,
                            );
                      }}
                    />
                    <label
                      htmlFor={`location_${index}`} // Make sure each label has a unique htmlFor
                      className="!text-[--text-color] !text-sm Poppins400 cursor-pointer"
                    >
                      {data.name}
                    </label>
                  </Box>
                )}
              />
            ))
        }
        {filterDisplay?.entertainmentType &&
          profileType === "ACT_PROFILE" &&
          actTypes?.length > 0 && (
            <Box className="flex justify-center">
              <Button
                className="flex gap-1 items-center !normal-case"
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
                onClick={handleShowMore}
              >
                {showAllReviews ? (
                  <>
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                      {t("showLess")}
                    </Typography>
                    <North className="text-xl text-[--text-color]" />
                  </>
                ) : (
                  <>
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                      {t("showMore")}
                    </Typography>
                    <South className="text-xl text-[--text-color]" />
                  </>
                )}
              </Button>
            </Box>
          )}

        {filterDisplay?.entertainmentType &&
          profileType === "ACT_PROFILE" &&
          actTypes?.length > 0 && (
            <Divider sx={{ borderTop: "thin solid var(--divider-color)" }} className="my-4" />
          )}
        {filterDisplay?.musicGenres && profileType === "ACT_PROFILE" && musicGenres?.length > 0 && (
          <Box className="flex justify-between mt-2">
            <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
              {a("musicGenre")}
            </Typography>
            <IconButton>
              <Clear
                className="text-[--text-color] text-lg"
                onClick={() => {
                  removeQueryParams(["musicGenre"], router, searchParams);
                }}
              />
            </IconButton>
          </Box>
        )}

        {filterDisplay?.musicGenres &&
          profileType === "ACT_PROFILE" &&
          (showAllReviewsMusicGenre ? musicGenres : musicGenres?.slice(0, 4))?.map(
            (data, index) => (
              <Controller
                key={index}
                name="musicGenre"
                control={control}
                render={({ field }) => (
                  <Box key={index} className="flex items-center !mb-2">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={parsedQueryParams?.musicGenre?.includes(data.name)}
                      value={data.name}
                      onChange={(e) => {
                        field.onChange(e.target.checked);
                        e.target.checked
                          ? appendQueryParams(
                              { musicGenre: [e.target.value].filter(Boolean) },
                              router,
                              searchParams,
                            )
                          : updateQueryParams("musicGenre", [e.target.value], router, searchParams);
                      }}
                    />
                    <label
                      htmlFor={`location_${index}`} // Make sure each label has a unique htmlFor
                      className="!text-[--text-color] !text-sm Poppins400 cursor-pointer"
                    >
                      {data.name}
                    </label>
                  </Box>
                )}
              />
            ),
          )}
        {filterDisplay?.musicGenres && profileType === "ACT_PROFILE" && musicGenres?.length > 0 && (
          <Box className="flex justify-center">
            <Button
              className="flex gap-1 items-center !normal-case"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={handleShowMoreMusicGenre}
            >
              {showAllReviewsMusicGenre ? (
                <>
                  <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                    {t("showLess")}
                  </Typography>
                  <North className="text-xl text-[--text-color]" />
                </>
              ) : (
                <>
                  <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                    {t("showMore")}
                  </Typography>
                  <South className="text-xl text-[--text-color]" />
                </>
              )}
            </Button>
          </Box>
        )}

        {filterDisplay?.musicGenres && profileType === "ACT_PROFILE" && musicGenres?.length > 0 && (
          <Divider sx={{ borderTop: "thin solid var(--divider-color)" }} className="my-4" />
        )}

        {filterDisplay?.rating && profileType !== "EVENT_PROFILE" && (
          <Box>
            <Box className="flex justify-between mt-2">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                {a("rating")}
              </Typography>
              <IconButton>
                <Clear
                  className="text-[--text-color] text-lg"
                  onClick={() => {
                    removeQueryParams(["overallRating"], router, searchParams);
                    setValue("overallRating", "");
                  }}
                />
              </IconButton>
            </Box>
            <Controller
              name="overallRating"
              control={control}
              render={({ field }) => (
                <RadioGroup
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    appendQueryParams({ overallRating: e.target.value }, router, searchParams);
                  }}
                >
                  {ratingValue.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data.value?.toString()}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <label className="flex gap-x-2" htmlFor={`radio-${id}`}>
                          {data?.title === "Without rating" ? (
                            " "
                          ) : (
                            <Rating value={data?.value} readOnly />
                          )}
                          <Typography className="!text-[--text-color] !normal-case Poppins400 !text-sm">
                            {data?.title}
                          </Typography>
                        </label>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
            <Divider sx={{ borderTop: "thin solid var(--divider-color)" }} className="mb-2" />
          </Box>
        )}
      </Box>
      <Box
        className={` ${isMobile ? "fixed flex justify-between bg-[--bg-color] items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "gap-x-4"}`}
      >
        <Button
          className=" w-5/12 h-[39px] flex lg:hidden !gap-x-2 items-center"
          onClick={handleClose}
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy !underline text-center !text-sm !leading-[15.4px] !text-[--text-color]">
            {p("cancel")}
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>
        <Button
          className="!bg-[--text-color] h-[39px] !flex !gap-x-2 items-center lg:!hidden lg:!mt-5 md:!mt-5"
          // onClick={handleSubmit(onSubmit)}
          onClick={handleClose}
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
            Search
          </Typography>
          <SouthEast className="text-[--bg-color] text-xl" />
        </Button>
      </Box>
    </>
  );

  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          sx={{
            "& .MuiPaper-root": {
              height: "95%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Box className="search-filter-container">{content}</Box>
      )}
    </>
  );
};

export default SearchFilter;
