"use client";
import { CommonImage } from "@/component";
import { Box, List, ListItem, ListItemButton, ListItemIcon, Typography } from "@mui/material";
import React, { useEffect } from "react";
import ActImage from "@/assets/png/ActImage.png";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import AccountSvg from "@/assets/svg/Account.svg";
import Setting from "@/assets/svg/Setting.svg";
import BlockListSvg from "@/assets/svg/BlockList.svg";
import { useDispatch, useSelector } from "react-redux";
import { useAppContext } from "@/context/app/app.context";
import { East } from "@mui/icons-material";
import LikeIcon from "@/assets/svg/Fav.svg";
import Logout from "@/assets/svg/Logout.svg";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { logout } from "@/store/slice/auth/login.auth.slice";

const AccountSidebar = () => {
  const lang = useLocale();
  const pathName = usePathname();
  //const { showSnackbar } = useSnackbar();
  const router = useRouter();
  // eslint-disable-next-line
  const { isOpened, setIsOpened } = useAppContext();

  // const handleLogout = () => {
  //   dispatch(logout())
  //     .unwrap()
  //     .then(() => {
  //       showSnackbar("Logout successfully", "success");
  //       router.replace(`/${lang}`);
  //     })
  //     .catch((error) => {
  //       showSnackbar(error, "error");
  //     });
  // };

  useEffect(() => {
    setIsOpened(false);
  }, []);

  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  const handleLogout = () => {
    dispatch(logout())
      .unwrap()
      .then(() => {
        showSnackbar("Logout successfully", "success");
        router.replace(`/${lang}`);
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  const { currentUser } = useSelector((state) => state.login);

  const Menu = [
    {
      id: 0,
      text: "Account information",
      icon: <AccountSvg className="!w-6 !h-6" />,
      path: `/${lang}/user/account-information`,
    },
    {
      id: 1,
      text: "Block list",
      icon: <BlockListSvg className="!w-6 !h-6" />,
      path: `/${lang}/user/block-list`,
    },
    {
      id: 2,
      text: "Favourites",
      icon: <LikeIcon className="!w-6 !h-6" />,
      path: `/${lang}/favourites/acts`,
    },
    ...(!currentUser?.socialLoginUser
      ? [
          {
            id: 3,
            text: "Settings",
            icon: <Setting className="!w-6 !h-6" />,
            path: `/${lang}/user/settings`,
          },
        ]
      : []),
  ];

  return (
    <Box
      className={`lg:fixed lg:!w-[274px] !w-full ml-0 bg-[--bg-color] lg:mt-36 z-10 lg:border-r border-r-[--divider-color] h-full transition-all duration-300 ${
        isOpened ? 'lg:ml-[300px]' : 'lg:ml-[100px]'
      }`}
    >
      <Box className=" flex lg:justify-center pb-8">
        <CommonImage
          src={ActImage}
          alt="act-image"
          className="rounded-full lg:border-[10px] border-[5px] border-[--bg-color] lg:w-40 lg:h-40 w-[62px] h-[62px] object-cover absolute right-0 lg:mx-auto lg:left-0 left-6 top-[95px] lg:-top-[75px] "
        />
      </Box>
      <Typography className="text-2xl lg:!py-20 !py-5 lg:px-0 px-8 !text-[--text-color] CraftworkGroteskMedium lg:text-center">
        Hello, <br />
        {currentUser?.firstName} {currentUser?.lastName}
      </Typography>
      <List sx={{ padding: 0 }}>
        {Menu.map((data) => (
          <ListItem key={data.id} sx={{ padding: 0 }}>
            <ListItemButton
              sx={{ padding: 0, border: 0 }}
              className={` hover:!bg-[--footer-bg] flex justify-between mx-4 lg:mx-0 lg:border-b-0 border-b border-b-[--divider-color] lg:rounded-[10px] !py-4 !px-4 ${
                pathName === data.path ? "lg:bg-[--footer-bg] bg-transparent" : ""
              }`}
              onClick={() => router.push(data.path)}
            >
              <Box className="flex items-center">
                <ListItemIcon
                  sx={{
                    "&.MuiListItemIcon-root": {
                      minWidth: 0,
                    },
                  }}
                >
                  {data.icon}
                </ListItemIcon>
                <Typography
                  className={`text-[--text-color] text-sm pl-4 ${
                    pathName === data.path ? "font-craftWorkHeavy" : "font-craftWorkRegular"
                  }`}
                >
                  {data.text}
                </Typography>
              </Box>
              <East className="text-[--text-color] text-lg lg:hidden inline" />
            </ListItemButton>
          </ListItem>
        ))}
        <Box
          className="justify-between items-center hover:!bg-[--footer-bg] lg:mx-0 lg:border-b-0 border-b border-b-[--divider-color] lg:rounded-[10px] !py-4 !px-4 mx-4 max-sm:flex hidden mb-2 cursor-pointer"
          onClick={handleLogout}
        >
          <Box className="flex items-center gap-3">
            <Logout className="!w-6 !h-6" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !text-center">
              Log Out
            </Typography>
          </Box>
          <East className="text-[--text-color] text-lg lg:hidden inline" />
        </Box>
      </List>
      {/* <Box
        className="flex gap-4 w-full mx-6 lg:mx-0 cursor-pointer absolute lg:bottom-56 bottom-4 items-center !py-4 !pl-5 "
        onClick={handleLogout}
      >
        <ProfilesSvg className="w-6 h-6" />
        <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
          Sign Out
        </Typography>
      </Box> */}
    </Box>
  );
};

export default AccountSidebar;
