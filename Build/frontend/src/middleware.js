import createMiddleware from "next-intl/middleware";
import { locales, localePrefix } from "./navigation";
import { getLocalStorage } from "@/utils";

export default createMiddleware({
  locales,
  localePrefix,
  defaultLocale: locales.includes(getLocalStorage("lang")) ? getLocalStorage("lang") : "en",
  localeDetection: false,
});

export const config = {
  // Match only internationalized pathnames
  //matcher: [ '/','/(fr|en|esp)/:path*','/((?!_next|_vercel|.*\\..*).*)']

  matcher:
    "/((?!api|_next/static|_next/image|favicon.ico|apple-touch-icon.png|favicon.svg|images/books|icons|manifest|poster-images).*)",
};
