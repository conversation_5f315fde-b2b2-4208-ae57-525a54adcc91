"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import { Box, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
import ProfileCard from "@/common/profile-card/profile-card.common";
import { useSelector, useDispatch } from "react-redux";
import { Button } from "@/component";
import { stringifyParams } from "@/utils";
import { getPrivateSearchData, getSearchData } from "@/store/slice/common/search.slice";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import SearchFilter from "@/common/search-filter/search-filter.common";
import SearchHeader from "@/common/search-header/search-header.common";
import dynamic from "next/dynamic";
import NothingSearch from "@/assets/svg/NothingSearch.svg";
import SavedSearchPopupIcon from "@/assets/svg/SavedSearchPopupIcon.svg";
import { SouthEast } from "@mui/icons-material";
import SavedSearchPopup from "@/common/saved-search-popup/saved-search-popup.common";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import usePaginate from "@/hooks/usePaginate";
import EventCard from "@/common/profile-card/event-card.common ";
import CardLoader from "@/component/loader/card-loader.component";
import { notificationList } from "@/store/slice/common/instantMessage.slice";
// import SavedSearchDrawer from "@/common/saved-search-drawer/saved-search-drawer.common";

//import MapSearch from "@/common/profile-card/map-search";
const MapSearch = dynamic(() => import("@/common/profile-card/map-search"), {
  ssr: false,
});

const Search = () => {
  const [isClient, setIsClient] = useState(false);
  //eslint-disable-next-line
  // const [isSearch, setIsSearch] = useState(false);
  const t = useTranslations("search");
  const s = useTranslations("searchFilter");
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const searchStrings = searchParams.get("searchStrings") || "";
  const searchName = searchParams.get("searchName") || "";
  const cityParam = searchParams.get("city") || "";
  const stateParam = searchParams.get("state") || "";
  const countryParam = searchParams.get("country") || "";
  const searchDateTypeParam = searchParams.get("searchDateType") || "";
  const startDateParam = searchParams.get("startDate") || "";
  const endDateParam = searchParams.get("endDate") || "";
  const distanceParam = searchParams.get("distance") || "";
  const entertainmentTypeParam = searchParams.getAll("entertainmentType").join(",") || "";
  const overallRatingParam = searchParams.get("overallRating") || "";
  const musicGenreParam = searchParams.getAll("musicGenre").join(",") || "";
  const profileType = searchParams.get("profileType") ?? "ACT_PROFILE";
  const type = searchParams.get("type") || "";
  const [saveSearch] = useState("");
  const { token } = useSelector((state) => state.login);
  const { setIsTokenExpired } = useAppContext();
  const [loading, setLoading] = useState(true);
  const { searchData } = useSelector((state) => state.search);
  const [open, setOpen] = useState(false);
  const [searchFilters, setSearchFilters] = useState({});
  //eslint-disable-next-line
  const { pageNo, search, onSearch } = usePaginate();

  const handleOpen = () => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  {
    /** page and size  => pagination */
  }
  const page = pageNo - 1 || 0;
  const size = 12;
  const params = stringifyParams({ page, size, searchString: searchStrings });
  const pageParams = { page, size };
  // const router = useRouter();
  const urlData = useMemo(() => {
    return Object.fromEntries(searchParams.entries());
  }, [searchParams]);
  // const isSearch = useMediaQuery(theme.breakpoints.down("md"));
  //const parsedQueryParams = typeof window !== "undefined" && queryString.parse(window?.location?.search);

  useEffect(() => {
    setLoading(true);
    const filter = {
      searchName: searchName ?? "",
      searchStrings: [searchStrings],
      searchFilter: {},
      searchType:
        profileType === "ACT_PROFILE"
          ? "SEARCH_TYPE_ACT"
          : profileType === "VENUE_PROFILE"
            ? "SEARCH_TYPE_VENUE"
            : profileType === "EVENT_PROFILE"
              ? "SEARCH_TYPE_EVENT"
              : "SEARCH_TYPE_VENUE",
    };

    if (urlData?.city || urlData?.state || urlData?.country || urlData?.distance) {
      filter.searchFilter = {
        ...filter.searchFilter,
        searchLocation: {
          cityName: urlData?.city,
          stateName: urlData?.state,
          countryName: urlData?.country,
          distance: urlData?.distance || 100,
        },
      };
    }

    if (
      (urlData?.searchDateType || urlData?.startDate || urlData?.endDate) &&
      profileType === "EVENT_PROFILE"
    ) {
      filter.searchFilter = {
        ...filter.searchFilter,
        searchDate: {
          searchDateType: urlData?.searchDateType,
          startDate: urlData?.startDate,
          endDate: urlData?.endDate,
        },
      };
    }

    if (entertainmentTypeParam && entertainmentTypeParam.split(",").length > 0) {
      filter.searchFilter = {
        ...filter.searchFilter,
        entertainmentTypesList: entertainmentTypeParam.split(",").map((item) => ({ name: item })),
      };
    }

    if (overallRatingParam) {
      filter.searchFilter = {
        ...filter.searchFilter,
        actRating: { overallRating: overallRatingParam },
      };
    }

    if (musicGenreParam && musicGenreParam.split(",").length > 0) {
      filter.searchFilter = {
        ...filter.searchFilter,
        musicGenreList: musicGenreParam.split(",").map((item) => ({ name: item })),
      };
    }

    // if (musicGenreParam && parsedQueryParams?.musicGenre) {
    //   filter.searchFilter = {
    //     ...filter.searchFilter,
    //     musicGenreList:
    //       typeof parsedQueryParams?.musicGenre === "string"
    //         ? [{ name: parsedQueryParams?.musicGenre }]
    //         : parsedQueryParams?.musicGenre.map((item) => ({ name: item })),
    //   };
    // }

    setSearchFilters(filter);
    if (token) {
      dispatch(getPrivateSearchData({ filter, profileType, pageParams }))
        .unwrap()
        .then(() => {
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    } else {
      dispatch(getSearchData({ filter, profileType, pageParams }))
        .unwrap()
        .then(() => {
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [
    cityParam,
    searchStrings,
    stateParam,
    countryParam,
    searchDateTypeParam,
    startDateParam,
    endDateParam,
    entertainmentTypeParam,
    overallRatingParam,
    musicGenreParam,
    saveSearch,
    profileType,
    page,
    distanceParam,
  ]);

  useEffect(() => {
    setIsClient(true);
    dispatch(notificationList());
  }, []);

  if (!isClient) {
    return null; // Render nothing on the server
  }

  // if (loading) {
  //   return <Loader />;
  // }
  return (
    <>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className="!flex">
        {token && (
          <Box className="!hidden lg:!block">
            <Sidebar />
          </Box>
        )}
        {token && (
          <Box className="!inline lg:!hidden">
            <MobileNavbar />
          </Box>
        )}
        <Box className="w-full">
          <Box
            className={` lg:pr-0 pl-4 pr-3 lg:!pt-24 !pt-8 !w-full ${token ? "lg:pl-32" : "lg:pl-0"}`}
          >
            <SearchHeader
              type={type}
              total={`${searchData?.numberOfElements} / ${searchData?.totalElements}`}
              setOpenSaveSearch={handleOpen}
            />
          </Box>
          <Box className="flex lg:h-[calc(100vh_-_150px)]">
            <SearchFilter type={type} />
            {loading ? (
              <CardLoader />
            ) : searchData?.content?.length > 0 ? (
              <Box className=" lg:pl-6 pt-0 lg:py-4 pl-0">
                <Box className="!flex !px-4 !justify-between">
                  <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
                    {t("title")}
                  </Typography>
                </Box>
                {type !== "MAP" && (
                  <Box className="mt-3">
                    {profileType === "EVENT_PROFILE" ? (
                      <EventCard
                        profiles={searchData}
                        type="search"
                        params={searchFilters}
                        profileType={profileType}
                        perPageRecords={size}
                      />
                    ) : (
                      <ProfileCard
                        profiles={searchData}
                        type="search"
                        params={searchFilters}
                        profileType={profileType}
                        perPageRecords={size}
                      />
                    )}
                  </Box>
                )}
                {type === "MAP" && (
                  <Box className="mt-3 lg:mb-0 mb-28 lg:h-[calc(100%_-_32px)]">
                    <MapSearch profiles={searchData} type="search" params={params} />
                  </Box>
                )}
              </Box>
            ) : (
              <Box className="flex justify-center w-full">
                <Box className=" pt-6 lg:pt-32 pl-4">
                  <Box className="flex flex-col items-center">
                    <NothingSearch className="w-12 h-12" />
                    <Typography className="text-2xl text-[--text-color] text-center CraftworkGroteskMedium">
                      {t("weAreSorry")}
                    </Typography>
                    <Typography className="text-sm text-[--text-color] text-center CraftworkGroteskRegular">
                      {t("specifiedFilter")}
                    </Typography>
                  </Box>
                  <Box className="mt-12 mb-24 relative p-3 mx-4 border border-[--text-color] lg:flex-row md:flex-col flex-col flex gap-2 rounded-[4px] max-w-3xl">
                    <SavedSearchPopupIcon className="w-12 h-12 md:relative absolute left-1 " />
                    <Box className="md:max-w-md max-w-sm lg:mt-0 mt-3">
                      <Typography className="text-[--text-color] lg:ml-0 ml-10 text-lg CraftworkGroteskMedium">
                        {t("saveThisSearch")}
                      </Typography>
                      <Typography className="text-[--text-color] md:pt-0 pt-3 text-sm CraftworkGroteskRegular">
                        {t("searchTerm")}
                      </Typography>
                    </Box>
                    <Button
                      onClick={handleOpen}
                      className="!normal-case !flex !gap-2"
                      sx={{
                        padding: 0,
                        "&.MuiButtonBase-root": {
                          justifyContent: "normal",
                          alignItems: "start",
                          color: "transparent !important",
                        },
                        "&.MuiButton-root:hover": {
                          backgroundColor: "transparent !important",
                        },
                      }}
                    >
                      <Typography className="text-sm CraftworkGroteskHeavy !text-[--text-color] underline">
                        {s("saveSearch")}
                      </Typography>
                      <SouthEast className="text-[--text-color] " />
                    </Button>
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
          <SavedSearchPopup open={open} handleClose={handleClose} filter={searchFilters} />
          {/* <SavedSearchDrawer open={open} filter={searchFilters} handleClose={handleClose} /> */}
        </Box>
      </Box>
    </>
  );
};

export default Search;
