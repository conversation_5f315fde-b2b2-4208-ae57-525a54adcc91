// @without_layout
// "use client";

// const Layout = ({ children }) => {
//   return <>{children}</>;
// };
// export default Layout;

// // ----

// @layout_with_sidebar

"use client";

import Sidebar from "@/common/sidebar/sidebar.common";
import { Box } from "@mui/material";
import { useAppContext } from "@/context/app/app.context";

const Layout = ({ children }) => {
  const { isOpened } = useAppContext();

  return (
    <div>
      <Box className="!hidden lg:!block">
        <Sidebar />
      </Box>

      {/* <Box className="!inline relative lg:!static lg:!hidden">
        <MobileNavbar />
      </Box> */}
      <Box
        className={`ml-[12px] transition-all duration-300 ${
          isOpened ? 'lg:ml-[300px]' : 'lg:ml-[100px]'
        }`}
      >
        {children}
      </Box>
    </div>
  );
};
export default Layout;
