<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Layout Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #181b1b;
            color: #efefef;
        }
        
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 72px;
            background-color: #181b1b;
            border-bottom: 1px solid rgba(76, 78, 79, 0.5);
            z-index: 30;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        
        .sidebar {
            position: fixed;
            top: 72px;
            left: 0;
            bottom: 0;
            width: 100px;
            background-color: #181b1b;
            border-right: 1px solid rgba(76, 78, 79, 0.5);
            z-index: 50;
            transition: width 0.3s ease;
        }
        
        .sidebar.expanded {
            width: 300px;
        }
        
        .content {
            margin-left: 100px;
            margin-top: 72px;
            padding: 20px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 72px);
        }
        
        .content.sidebar-expanded {
            margin-left: 300px;
        }
        
        .toggle-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #efefef;
            color: #181b1b;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .test-box {
            background-color: #202222;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid rgba(76, 78, 79, 0.5);
        }
    </style>
</head>
<body>
    <div class="navbar">
        <h3>StageMinder - Layout Test</h3>
    </div>
    
    <div class="sidebar" id="sidebar">
        <button class="toggle-btn" onclick="toggleSidebar()">Toggle</button>
    </div>
    
    <div class="content" id="content">
        <h1>Content Area</h1>
        <div class="test-box">
            <h2>Test Box 1</h2>
            <p>This content should not be hidden by the sidebar when it expands.</p>
            <p>Current sidebar state: <span id="status">Collapsed (100px)</span></p>
        </div>
        
        <div class="test-box">
            <h2>Test Box 2</h2>
            <p>Click the "Toggle" button in the sidebar to test the layout.</p>
            <p>The content should smoothly adjust its left margin when the sidebar expands/collapses.</p>
        </div>
        
        <div class="test-box">
            <h2>Expected Behavior</h2>
            <ul>
                <li>Collapsed: Content has 100px left margin</li>
                <li>Expanded: Content has 300px left margin</li>
                <li>Smooth transition between states</li>
                <li>No content should be hidden behind the sidebar</li>
            </ul>
        </div>
    </div>
    
    <script>
        let isExpanded = false;
        
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const status = document.getElementById('status');
            
            isExpanded = !isExpanded;
            
            if (isExpanded) {
                sidebar.classList.add('expanded');
                content.classList.add('sidebar-expanded');
                status.textContent = 'Expanded (300px)';
            } else {
                sidebar.classList.remove('expanded');
                content.classList.remove('sidebar-expanded');
                status.textContent = 'Collapsed (100px)';
            }
        }
    </script>
</body>
</html>
