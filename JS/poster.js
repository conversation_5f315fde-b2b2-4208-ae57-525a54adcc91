let canvas;
let backgroundImage = null;
let styleLayer = null;
let currentWidth = 648;
let currentHeight = 900;
let selectedSize = null;

// Initialize Fabric canvas
function initCanvas() {
    canvas = new fabric.Canvas('posterCanvas', {
        width: currentWidth,
        height: currentHeight,
        preserveObjectStacking: true,
        selection: true,
        selectionColor: 'rgba(100, 100, 255, 0.3)',
        selectionLineWidth: 2
    });
    
    // Set up delete functionality
    setupDeleteFunctionality();
    
    // Set up layer management
    setupLayerManagement();
    
    // Set up selection handlers
    setupSelectionHandlers();
    
    // Load initial background and style layer
    loadBackgroundImage('../image/10-4.jpg');
    
    // 等待一小段时间后再加载样式层，确保背景图片已加载
    setTimeout(() => {
        loadStyleLayer('../image/Poster2-StyleLayer.png');
        // 初始化文本元素
        initializeTextElements();
    }, 100);
}

// Set up delete functionality
function setupDeleteFunctionality() {
    // Delete button click handler
    const deleteBtn = document.getElementById('deleteSelected');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            deleteSelectedObjects();
        });
    }

    // Delete key handler
    document.addEventListener('keyup', function(e) {
        if ((e.key === 'Delete' || e.key === 'Backspace') && canvas.getActiveObject()) {
            deleteSelectedObjects();
        }
    });
}

// Delete selected objects
function deleteSelectedObjects() {
    const activeObject = canvas.getActiveObject();
    if (!activeObject) return;

    if (activeObject.type === 'activeSelection') {
        const activeGroup = activeObject;
        canvas.discardActiveObject();
        activeGroup.getObjects().forEach(function(obj) {
            canvas.remove(obj);
            // Update references if background or style layer is deleted
            if (obj === backgroundImage) {
                backgroundImage = null;
            }
            if (obj === styleLayer) {
                styleLayer = null;
            }
        });
    } else {
        canvas.remove(activeObject);
        // Update references if background or style layer is deleted
        if (activeObject === backgroundImage) {
            backgroundImage = null;
        }
        if (activeObject === styleLayer) {
            styleLayer = null;
        }
    }
    canvas.requestRenderAll();
}

// Load background image
function loadBackgroundImage(url) {
    fabric.Image.fromURL(url, function(img) {
        if (backgroundImage) {
            canvas.remove(backgroundImage);
        }
        
        img.scaleToWidth(canvas.width);
        if (img.height * img.scaleY > canvas.height * 0.56) {
            img.scaleToHeight(canvas.height * 0.56);
        }
        
        img.set({
            left: 0,
            top: 0,
            selectable: true,
            hasControls: true,
            hasBorders: true,
            name: 'backgroundImage',
            excludeFromExport: false
        });
        
        backgroundImage = img;
        canvas.add(backgroundImage);
        
        // Ensure proper layer ordering
        canvas.sendToBack(backgroundImage);
        if (styleLayer) {
            canvas.bringForward(styleLayer);
        }
        
        // Ensure text layer stays on top
        const textLayer = canvas.getObjects().find(obj => obj.name === 'textLayer');
        if (textLayer) {
            canvas.bringToFront(textLayer);
        }
        
        canvas.requestRenderAll();
    });
}

// Load style layer
function loadStyleLayer(url) {
    fabric.Image.fromURL(url, function(img) {
        if (styleLayer) {
            canvas.remove(styleLayer);
        }
        
        // 设置初始属性
        img.set({
            left: 0,
            top: 0,
            selectable: false,
            hasControls: false,
            hasBorders: false,
            lockMovementX: true,
            lockMovementY: true,
            lockRotation: true,
            lockScalingX: true,
            lockScalingY: true,
            lockSkewingX: true,
            lockSkewingY: true,
            opacity: 0.8,
            name: 'styleLayer',
            excludeFromExport: false,
            evented: false
        });
        
        // 确保样式层填满整个画布
        img.scaleToWidth(canvas.width);
        if (img.getScaledHeight() < canvas.height) {
            img.scaleToHeight(canvas.height);
        }
        
        styleLayer = img;
        canvas.add(styleLayer);
        
        // 确保正确的图层顺序
        if (backgroundImage) {
            canvas.sendToBack(backgroundImage);
        }
        canvas.bringForward(styleLayer);
        
        // 确保所有文本保持在顶层
        canvas.getObjects().forEach(obj => {
            if (obj.textLayer) {
                canvas.bringToFront(obj);
            }
        });
        
        canvas.requestRenderAll();
    });
}

// Initialize text elements
function initializeTextElements() {
    // Initialize text objects with different comfortable colors
    const colors = {
        mainTitle: '#4A90E2',    // 清新蓝色
        subtitle: '#50B794',     // 舒适绿色
        location: '#9B6EDD',     // 淡紫色
        eventDetails: '#FF9666', // 暖橙色
        venueInfo: '#5EADF0'     // 天蓝色
    };
    
    // Main Title
    const mainTitle = new fabric.Text(document.getElementById('mainTitle').value, {
        left: canvas.width / 2,
        top: 520,
        fontSize: 70,
        fontFamily: 'Arial',
        fill: colors.mainTitle,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'mainTitle'
    });
    canvas.add(mainTitle);

    // Subtitle
    const subtitle = new fabric.Text(document.getElementById('subtitle').value, {
        left: canvas.width / 2,
        top: 620,
        fontSize: 32,
        fontFamily: 'Arial',
        fill: colors.subtitle,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'subtitle'
    });
    canvas.add(subtitle);

    // Location
    const location = new fabric.Text(document.getElementById('location').value, {
        left: canvas.width / 2,
        top: 670,
        fontSize: 50,
        fontFamily: 'Arial',
        fill: colors.location,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'location'
    });
    canvas.add(location);

    // Event Details
    const eventDate = new fabric.Text(document.getElementById('eventDate').value, {
        left: canvas.width / 2 - 150,
        top: 750,
        fontSize: 22,
        fontFamily: 'Arial',
        fill: colors.eventDetails,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'eventDate'
    });

    const eventTime = new fabric.Text(document.getElementById('eventTime').value, {
        left: canvas.width / 2,
        top: 750,
        fontSize: 22,
        fontFamily: 'Arial',
        fill: colors.eventDetails,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'eventTime'
    });

    const eventPrice = new fabric.Text(document.getElementById('eventPrice').value, {
        left: canvas.width / 2 + 150,
        top: 750,
        fontSize: 22,
        fontFamily: 'Arial',
        fill: colors.eventDetails,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'eventPrice'
    });

    canvas.add(eventDate);
    canvas.add(eventTime);
    canvas.add(eventPrice);

    // Venue Info
    const venueName = new fabric.Text(document.getElementById('venueName').value, {
        left: canvas.width / 2,
        top: 840,
        fontSize: 18,
        fontFamily: 'Arial',
        fill: colors.venueInfo,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'venueName'
    });

    const venueAddress = new fabric.Text(document.getElementById('venueAddress').value, {
        left: canvas.width / 2,
        top: 865,
        fontSize: 18,
        fontFamily: 'Arial',
        fill: colors.venueInfo,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'venueAddress'
    });

    const venueWebsite = new fabric.Text(document.getElementById('venueWebsite').value, {
        left: canvas.width / 2,
        top: 890,
        fontSize: 18,
        fontFamily: 'Arial',
        fill: colors.venueInfo,
        originX: 'center',
        originY: 'top',
        textAlign: 'center',
        textLayer: true,
        name: 'venueWebsite'
    });

    canvas.add(venueName);
    canvas.add(venueAddress);
    canvas.add(venueWebsite);

    // Update color pickers with initial colors
    $('#mainTitleColor').spectrum('set', colors.mainTitle);
    $('#subtitleColor').spectrum('set', colors.subtitle);
    $('#locationColor').spectrum('set', colors.location);
    $('#eventDetailsColor').spectrum('set', colors.eventDetails);
    $('#venueInfoColor').spectrum('set', colors.venueInfo);

    canvas.renderAll();
}

// Function to update controls based on selected object
function updateControlsFromSelection(obj) {
    if (!obj) return;
    
    if (obj.type === 'text') {
        // Update text controls
        const text = obj.text;
        if (text === $('#mainTitle').val()) {
            $('#mainTitleColor').spectrum('set', obj.fill);
            $('#mainTitleFont').val(obj.fontFamily);
            $('#mainTitleSize').val(obj.fontSize);
            $('#mainTitleSizeValue').text(obj.fontSize);
        } else if (text === $('#subtitle').val()) {
            $('#subtitleColor').spectrum('set', obj.fill);
            $('#subtitleFont').val(obj.fontFamily);
            $('#subtitleSize').val(obj.fontSize);
            $('#subtitleSizeValue').text(obj.fontSize);
        }
        // Add similar conditions for other text elements
    }
}

// Update text properties
function updateText(id, text, props = {}) {
    const textObj = canvas.getObjects().find(obj => obj.name === id);
    if (textObj) {
        if (text) textObj.set('text', text);
        textObj.set(props);
        canvas.requestRenderAll();
    }
}

// Handle font changes
function handleFontChange(element, textId) {
    $(element).on('change', function(e) {
        updateText(textId, undefined, { fontFamily: e.target.value });
    });
}

// Handle color changes
$('[data-type="color"]').spectrum({
    type: "color",
    showInput: true,
    showInitial: true,
    showAlpha: false,
    showButtons: false,
    move: function(color) {
        const id = this.id;
        const textId = id.replace('Color', '');
        const objects = canvas.getObjects();
        const textObj = objects.find(o => {
            if (o.type === 'text') {
                return o.text === $('#' + textId).val();
            }
            return false;
        });
        
        if (textObj) {
            textObj.set('fill', color.toHexString());
            canvas.requestRenderAll();
        }
    }
});

// Handle image upload with cropping
document.getElementById('imageInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(event) {
        // Show crop modal first
        const modal = document.getElementById('cropModal');
        const cropImage = document.getElementById('cropImage');
        
        modal.style.display = 'block';
        cropImage.src = event.target.result;
        
        // Initialize cropper
        if (window.cropper) {
            window.cropper.destroy();
        }
        
        window.cropper = new Cropper(cropImage, {
            aspectRatio: canvas.width / (canvas.height * 0.56), // 56% of canvas height for background
            viewMode: 1,
            dragMode: 'move',
            autoCropArea: 1,
            restore: false,
            modal: true,
            guides: true,
            highlight: true,
            cropBoxMovable: true,
            cropBoxResizable: true,
            toggleDragModeOnDblclick: true
        });
    };
    reader.readAsDataURL(file);
});

function addImagePreview(croppedCanvas) {
    const previewContainer = document.querySelector('.preview-container');
    const previewItem = document.createElement('div');
    previewItem.className = 'preview-item';
    
    // Create preview image
    const previewImage = document.createElement('img');
    previewImage.src = croppedCanvas.toDataURL();
    
    // Create delete button
    const deleteButton = document.createElement('button');
    deleteButton.className = 'preview-delete';
    deleteButton.innerHTML = '<i class="fas fa-times"></i>';
    
    // Add delete functionality
    deleteButton.addEventListener('click', function() {
        // Remove from canvas
        const objects = canvas.getObjects();
        const imageObject = objects.find(obj => obj.previewId === previewItem.dataset.previewId);
        if (imageObject) {
            canvas.remove(imageObject);
            canvas.requestRenderAll();
        }
        // Remove preview
        previewItem.remove();
    });
    
    // Generate unique ID for this preview
    const previewId = 'preview_' + Date.now();
    previewItem.dataset.previewId = previewId;
    
    // Add elements to preview item
    previewItem.appendChild(previewImage);
    previewItem.appendChild(deleteButton);
    previewContainer.appendChild(previewItem);
    
    return previewId;
}

// Modify the cropButton click handler
document.getElementById('cropButton').addEventListener('click', function() {
    if (!window.cropper) return;

    const croppedCanvas = window.cropper.getCroppedCanvas({
        width: canvas.width * 0.56,
        height: canvas.height * 0.56
    });

    // Add preview and get preview ID
    const previewId = addImagePreview(croppedCanvas);

    // Create fabric image and add to canvas
    fabric.Image.fromURL(croppedCanvas.toDataURL(), function(img) {
        img.set({
            left: 0,
            top: 0,
            previewId: previewId // Store reference to preview
        });
        img.scaleToWidth(canvas.width);
        canvas.add(img);
        canvas.requestRenderAll();
        
        // Send to back
        img.sendToBack();
    });

    // Close modal
    const modal = document.getElementById('cropModal');
    modal.style.display = 'none';
    window.cropper.destroy();
    window.cropper = null;
});

// Download poster
function downloadPoster() {
    const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 1
    });
    
    const link = document.createElement('a');
    link.download = 'poster.png';
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Initialize everything when the page loads
window.addEventListener('load', initCanvas);

// Style layer controls
document.addEventListener('DOMContentLoaded', function() {
    // Style layer opacity control
    const styleLayerOpacity = document.getElementById('styleLayerOpacity');
    const toggleStyleLayer = document.getElementById('toggleStyleLayer');

    // Wait for canvas to be initialized
    setTimeout(() => {
        if (styleLayerOpacity) {
            styleLayerOpacity.addEventListener('input', function() {
                const opacity = parseInt(this.value) / 100;
                document.getElementById('styleLayerOpacityValue').textContent = this.value;

                if (styleLayer) {
                    styleLayer.set('opacity', opacity);
                    canvas.requestRenderAll();
                }
            });
        }

        if (toggleStyleLayer) {
            toggleStyleLayer.addEventListener('click', function() {
                if (styleLayer) {
                    const isVisible = styleLayer.visible !== false;
                    styleLayer.set('visible', !isVisible);
                    canvas.requestRenderAll();
                    this.textContent = isVisible ? 'Show Style Layer' : 'Hide Style Layer';
                } else {
                    // 如果样式层还没加载，重新加载它
                    loadStyleLayer('../image/Poster2-StyleLayer.png');
                }
            });
        }
    }, 1000);
});

// Reset functionality
document.getElementById('resetButton').addEventListener('click', function() {
    canvas.clear();
    initCanvas();
});

// Social sharing functions
function shareToFacebook() {
    // Create Facebook share URL with current page URL
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`;
    // Open Facebook share dialog in a new window
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareToInstagram() {
    // Open Instagram create post page
    window.open('https://www.instagram.com/create/select/', '_blank', 'noopener,noreferrer');
}

// Document ready function
$(document).ready(function() {
    // Initialize color pickers with Fabric.js integration
    $('input[data-type="color"]').spectrum({
        type: "component",
        showInput: true,
        showInitial: true,
        showAlpha: true,
        allowEmpty: false,
        preferredFormat: "hex",
        change: function(color) {
            const id = this.id;
            const textId = id.replace('Color', '');
            updateText($('#' + textId).val(), undefined, { fill: color.toHexString() });
        }
    });

    // Function to update text content and style
    function updateText(inputId, textClass) {
        const input = $(`#${inputId}`);
        const colorInput = $(`#${inputId}Color`);
        const fontSelect = $(`#${inputId}Font`);
        const sizeSlider = $(`#${inputId}Size`);
        const sizeValue = $(`#${inputId}SizeValue`);

        function updateStyle() {
            const text = input.val();
            const color = colorInput.val();
            const font = fontSelect.val();
            const fontSize = parseInt(sizeSlider.val());
            
            // Update Fabric.js objects
            const objects = canvas.getObjects();
            const textObj = objects.find(o => o.type === 'text' && o.text === text);
            if (textObj) {
                textObj.set({
                    fill: color,
                    fontFamily: font,
                    fontSize: fontSize
                });
                canvas.requestRenderAll();
            }
            
            sizeValue.text(fontSize);
        }

        input.on('input', function() {
            const oldText = input.data('oldText');
            const newText = input.val();
            const objects = canvas.getObjects();
            const textObj = objects.find(o => o.type === 'text' && o.text === oldText);
            if (textObj) {
                textObj.set('text', newText);
                canvas.requestRenderAll();
            }
            input.data('oldText', newText);
        });
        
        colorInput.on('change', updateStyle);
        fontSelect.on('change', updateStyle);
        sizeSlider.on('input', updateStyle);
        
        // Store initial text value
        input.data('oldText', input.val());
    }

    // Update main title
    updateText('mainTitle', 'main-title');

    // Update subtitle
    updateText('subtitle', 'subtitle');

    // Update location
    updateText('location', 'location');

    // Update event details
    const eventDetailsFont = $('#eventDetailsFont');
    const eventDetailsSize = $('#eventDetailsSize');
    function updateEventDetails() {
        const font = eventDetailsFont.val();
        const color = $('#eventDetailsColor').val();
        const fontSize = eventDetailsSize.val();
        $('.info-label, .info-value').css({
            'color': color,
            'font-family': font
        });
        $('.info-label').css('font-size', `${fontSize * 0.8}px`);
        $('.info-value').css('font-size', `${fontSize}px`);
        $('#eventDetailsSizeValue').text(fontSize);
    }
    $('#eventDate, #eventTime, #eventPrice').on('input', function() {
        const type = this.id.replace('event', '').toUpperCase();
        $(`.info-item:contains('${type}')`).find('.info-value').text($(this).val());
    });
    eventDetailsFont.on('change', updateEventDetails);
    $('#eventDetailsColor').on('change', updateEventDetails);
    eventDetailsSize.on('input', updateEventDetails);

    // Update venue information
    const venueInfoFont = $('#venueInfoFont');
    const venueInfoSize = $('#venueInfoSize');
    function updateVenueInfo() {
        const font = venueInfoFont.val();
        const color = $('#venueInfoColor').val();
        const size = venueInfoSize.val();
        $('#venueInfoSizeValue').text(size);
        $('.venue-name, .venue-address, .venue-website').css({
            'color': color,
            'font-family': font,
            'font-size': `${size}px`
        });
    }
    $('#venueName').on('input', function() {
        $('.venue-name').text($(this).val());
    });
    $('#venueAddress').on('input', function() {
        $('.venue-address').text($(this).val());
    });
    $('#venueWebsite').on('input', function() {
        $('.venue-website').text($(this).val());
    });
    venueInfoFont.on('change', updateVenueInfo);
    $('#venueInfoColor').on('change', updateVenueInfo);
    venueInfoSize.on('input', updateVenueInfo);

    // Store initial values for reset functionality
    const initialValues = {
        mainTitle: 'WILD HARMONIES',
        mainTitleColor: '#ffffff',
        mainTitleFont: 'Arial',
        mainTitleSize: 70,
        subtitle: 'A FULL NIGHT OF ROCK, LIVE IN',
        subtitleColor: '#ffffff',
        subtitleFont: 'Arial',
        subtitleSize: 32,
        location: 'OTTAWA',
        locationColor: '#ffffff',
        locationFont: 'Arial',
        locationSize: 50,
        eventDate: 'SEP 25 2025',
        eventTime: '9:00 PM',
        eventPrice: '$23',
        eventDetailsColor: '#ffffff',
        eventDetailsFont: 'Arial',
        eventDetailsSize: 22,
        venueName: 'HOUSE OF TARG',
        venueAddress: '1077 BANK ST, OTTAWA',
        venueWebsite: 'HOUSEOFTARG.COM',
        venueInfoColor: '#ffffff',
        venueInfoFont: 'Arial',
        venueInfoSize: 18
    };

    // Set initial values
    Object.entries(initialValues).forEach(([id, value]) => {
        const element = $(`#${id}`);
        if (id.includes('Color')) {
            element.spectrum('set', value);
        } else {
            element.val(value);
        }
        if (id.includes('Font')) {
            element.trigger('change');
        } else if (!id.includes('Color')) {
            element.trigger('input');
        }
    });

    // Reset button functionality
    $('#resetButton').click(function() {
        Object.entries(initialValues).forEach(([id, value]) => {
            const element = $(`#${id}`);
            if (id.includes('Color')) {
                element.spectrum('set', value);
            } else {
                element.val(value);
            }
            if (id.includes('Font')) {
                element.trigger('change');
            } else if (!id.includes('Color')) {
                element.trigger('input');
            }
        });

        // Reset image if any
        const defaultImage = '../image/10-4.jpg';
        $('.background-image').attr('src', defaultImage);
        $('#imagePreview').attr('src', '').hide();
        $('#imageInput').val('');
    });

    // Image cropping functionality
    let cropper = null;
    const modal = document.getElementById('cropModal');
    const cropImage = document.getElementById('cropImage');
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');

    // Handle file input change
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Show crop modal
                modal.style.display = 'block';
                cropImage.src = e.target.result;
                
                // Initialize cropper
                if (cropper) {
                    cropper.destroy();
                }
                cropper = new Cropper(cropImage, {
                    aspectRatio: 4/3,
                    viewMode: 1,
                    dragMode: 'move',
                    autoCropArea: 1,
                    restore: false,
                    modal: true,
                    guides: true,
                    highlight: true,
                    cropBoxMovable: true,
                    cropBoxResizable: true,
                    toggleDragModeOnDblclick: true,
                    ready: function() {
                        // Ensure proper sizing
                        const containerData = cropper.getContainerData();
                        const canvasData = cropper.getCanvasData();
                        
                        // Set canvas data to fit container while maintaining aspect ratio
                        const scale = Math.min(
                            containerData.width / canvasData.width,
                            containerData.height / canvasData.height
                        );
                        
                        const width = canvasData.width * scale;
                        const height = canvasData.height * scale;
                        
                        cropper.setCanvasData({
                            left: (containerData.width - width) / 2,
                            top: (containerData.height - height) / 2,
                            width: width,
                            height: height
                        });
                        
                        // Set crop box to match canvas size
                        cropper.setCropBoxData({
                            left: (containerData.width - width) / 2,
                            top: (containerData.height - height) / 2,
                            width: width,
                            height: height
                        });
                    }
                });
            };
            reader.readAsDataURL(file);
        }
    });

    // Handle crop button click
    document.getElementById('cropButton').addEventListener('click', function() {
        if (!cropper) return;
        
        // Get cropped canvas
        const canvas = cropper.getCroppedCanvas({
            width: 648,
            height: 504,
            fillColor: '#fff',
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high',
        });
        
        // Convert to data URL
        const croppedImage = canvas.toDataURL('image/jpeg', 1.0);
        
        // Update preview and poster image
        imagePreview.src = croppedImage;
        imagePreview.style.display = 'block';
        $('.background-image').attr('src', croppedImage);
        
        // Close modal and cleanup
        modal.style.display = 'none';
        cropper.destroy();
        cropper = null;
    });

    // Handle rotate buttons
    document.getElementById('rotateLeft').addEventListener('click', function() {
        if (cropper) cropper.rotate(-90);
    });

    document.getElementById('rotateRight').addEventListener('click', function() {
        if (cropper) cropper.rotate(90);
    });

    // Handle cancel button
    document.getElementById('cancelCrop').addEventListener('click', function() {
        modal.style.display = 'none';
        if (cropper) {
            cropper.destroy();
            cropper = null;
        }
        imageInput.value = '';
    });

    // Handle modal close button
    document.querySelector('.close-modal').addEventListener('click', function() {
        modal.style.display = 'none';
        if (cropper) {
            cropper.destroy();
            cropper = null;
        }
        imageInput.value = '';
    });
});

// Set up layer management
function setupLayerManagement() {
    const bringForwardBtn = document.getElementById('bringForward');
    if (bringForwardBtn) {
        bringForwardBtn.addEventListener('click', function() {
            const activeObject = canvas.getActiveObject();
            if (!activeObject || activeObject.textLayer) return; // Don't allow moving text layers

            const objects = canvas.getObjects();
            const currentIndex = objects.indexOf(activeObject);
            
            if (currentIndex >= objects.length - 1) return;
            
            canvas.moveTo(activeObject, currentIndex + 1);
            
            // Ensure text stays on top
            canvas.getObjects().filter(obj => obj.textLayer).forEach(obj => {
                canvas.bringToFront(obj);
            });
            
            canvas.requestRenderAll();
        });
    }

    const sendBackwardBtn = document.getElementById('sendBackward');
    if (sendBackwardBtn) {
        sendBackwardBtn.addEventListener('click', function() {
            const activeObject = canvas.getActiveObject();
            if (!activeObject || activeObject.textLayer) return; // Don't allow moving text layers

            const objects = canvas.getObjects();
            const currentIndex = objects.indexOf(activeObject);
            
            if (currentIndex <= 0) return;
            
            canvas.moveTo(activeObject, currentIndex - 1);
            
            // Ensure text stays on top
            canvas.getObjects().filter(obj => obj.textLayer).forEach(obj => {
                canvas.bringToFront(obj);
            });
            
            canvas.requestRenderAll();
        });
    }
}

// Set up selection handlers
function setupSelectionHandlers() {
    canvas.on('selection:created', function(e) {
        if (e.selected && e.selected[0]) {
            updateControlsFromSelection(e.selected[0]);
        }
    });

    canvas.on('selection:updated', function(e) {
        if (e.selected && e.selected[0]) {
            updateControlsFromSelection(e.selected[0]);
        }
    });

    canvas.on('selection:cleared', function() {
        // Optional: Handle when selection is cleared
    });
}

// Add this function after initCanvas()
function resizeCanvas(width, height) {
    // Store current canvas content
    const json = canvas.toJSON();
    const oldWidth = canvas.width;
    const oldHeight = canvas.height;
    
    // Update canvas dimensions
    canvas.setWidth(width);
    canvas.setHeight(height);
    currentWidth = width;
    currentHeight = height;
    
    // Clear and load content back
    canvas.loadFromJSON(json, function() {
        // Calculate scale factors
        const scaleX = width / oldWidth;
        const scaleY = height / oldHeight;
        
        canvas.getObjects().forEach(obj => {
            // Scale position
            obj.left = obj.left * scaleX;
            obj.top = obj.top * scaleY;
            
            // Handle text objects differently
            if (obj.type === 'text') {
                // Scale font size proportionally
                obj.set('fontSize', obj.fontSize * Math.min(scaleX, scaleY));
            } else {
                // Scale other objects normally
                obj.set({
                    scaleX: obj.scaleX * scaleX,
                    scaleY: obj.scaleY * scaleY
                });
            }
            
            obj.setCoords();
        });
        
        // Update background image
        if (backgroundImage) {
            backgroundImage.set({
                left: 0,
                top: 0
            });
            
            // Scale to width first
            backgroundImage.scaleToWidth(width);
            
            // If height is too large, scale to height instead
            if (backgroundImage.height * backgroundImage.scaleY > height * 0.56) {
                backgroundImage.scaleToHeight(height * 0.56);
                
                // Center horizontally if width is now less than canvas width
                if (backgroundImage.width * backgroundImage.scaleX < width) {
                    backgroundImage.left = (width - backgroundImage.width * backgroundImage.scaleX) / 2;
                }
            }
        }
        
        // Update style layer
        if (styleLayer) {
            styleLayer.set({
                left: 0,
                top: 0
            });
            
            // 确保样式层填满整个画布
            styleLayer.scaleToWidth(width);
            if (styleLayer.getScaledHeight() < height) {
                styleLayer.scaleToHeight(height);
            }
            
            // 如果宽度现在大于画布宽度，居中处理
            if (styleLayer.getScaledWidth() > width) {
                styleLayer.left = (width - styleLayer.getScaledWidth()) / 2;
            }
        }
        
        // Ensure proper layer ordering
        if (backgroundImage) canvas.sendToBack(backgroundImage);
        if (styleLayer) {
            canvas.bringForward(styleLayer);
        }
        
        // Bring all text objects to front
        canvas.getObjects().filter(obj => obj.textLayer).forEach(obj => {
            canvas.bringToFront(obj);
        });
        
        canvas.renderAll();
    });
    
    // Update poster container size
    const posterElement = document.getElementById('poster');
    posterElement.style.width = width + 'px';
    posterElement.style.height = height + 'px';
}

// Add resize modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const resizeModal = document.getElementById('resizeModal');
    const resizeButton = document.getElementById('resizeButton');
    const cancelResize = document.getElementById('cancelResize');
    const applyResize = document.getElementById('applyResize');
    const sizeOptions = document.querySelectorAll('.size-option');
    
    // Show modal
    resizeButton.addEventListener('click', function() {
        resizeModal.style.display = 'block';
    });
    
    // Handle size option selection
    sizeOptions.forEach(option => {
        option.addEventListener('click', function() {
            sizeOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedSize = {
                width: parseInt(this.dataset.width),
                height: parseInt(this.dataset.height)
            };
        });
    });
    
    // Apply resize
    applyResize.addEventListener('click', function() {
        if (selectedSize) {
            resizeCanvas(selectedSize.width, selectedSize.height);
            resizeModal.style.display = 'none';
            selectedSize = null;
            sizeOptions.forEach(opt => opt.classList.remove('selected'));
        }
    });
    
    // Cancel resize
    cancelResize.addEventListener('click', function() {
        resizeModal.style.display = 'none';
        selectedSize = null;
        sizeOptions.forEach(opt => opt.classList.remove('selected'));
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === resizeModal) {
            resizeModal.style.display = 'none';
            selectedSize = null;
            sizeOptions.forEach(opt => opt.classList.remove('selected'));
        }
    });
}); 